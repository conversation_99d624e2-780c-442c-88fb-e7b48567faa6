<?php
/**
 * Test script to validate institution code fixes
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Include the functions from avans.php
function validateAndMapInstitutionCode($institutionCode) {
    if (empty($institutionCode)) {
        return null;
    }

    // Mapare pentru codurile de instituții care pot fi diferite în SOAP API
    $institutionMapping = [
        // Mapări cunoscute pentru SOAP API
        'InaltaCurtedeCASSATIESIJUSTITIE' => null, // Înalta Curte poate să nu fie suportată direct
        'CurteadeApelBUCURESTI' => 'CurteadeApelBUCURESTI',
        'TribunalulBUCURESTI' => 'TribunalulBUCURESTI',
        'JudecatoriaSECTORUL1BUCURESTI' => 'JudecatoriaSECTORUL1BUCURESTI',
        
        // Adăugăm mapări pentru alte instituții comune
        'TribunalulCLUJ' => 'TribunalulCLUJ',
        'CurteadeApelCLUJ' => 'CurteadeApelCLUJ',
        'TribunalulTIMIS' => 'TribunalulTIMIS',
        'CurteadeApelTIMISOARA' => 'CurteadeApelTIMISOARA'
    ];

    // Verificăm dacă avem o mapare specifică
    if (array_key_exists($institutionCode, $institutionMapping)) {
        return $institutionMapping[$institutionCode];
    }

    // Dacă nu avem mapare specifică, returnăm codul original
    return $institutionCode;
}

function filterResultsByInstitution($results, $institutionCode) {
    if (empty($institutionCode) || empty($results)) {
        return $results;
    }

    $institutii = getInstanteList();
    $institutionName = $institutii[$institutionCode] ?? '';

    return array_filter($results, function($result) use ($institutionCode, $institutionName) {
        $resultInstitution = $result->institutie ?? '';
        
        // Verificăm match exact cu codul
        if ($resultInstitution === $institutionCode) {
            return true;
        }
        
        // Verificăm match cu numele instituției
        if (!empty($institutionName) && stripos($resultInstitution, $institutionName) !== false) {
            return true;
        }
        
        // Verificăm match parțial cu codul
        if (stripos($resultInstitution, $institutionCode) !== false) {
            return true;
        }
        
        return false;
    });
}

echo "<h1>Institution Code Fixes Validation</h1>\n";
echo "<style>
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
    .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
    .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>\n";

// Test 1: Institution Code Mapping
echo "<div class='test-section info'>";
echo "<h2>Test 1: Institution Code Mapping</h2>\n";

$testCodes = [
    'InaltaCurtedeCASSATIESIJUSTITIE',
    'CurteadeApelBUCURESTI',
    'TribunalulBUCURESTI',
    'JudecatoriaSECTORUL1BUCURESTI',
    'TribunalulCLUJ',
    'NonExistentCode'
];

echo "<table>";
echo "<tr><th>Original Code</th><th>Mapped Code</th><th>Status</th></tr>";

foreach ($testCodes as $code) {
    $mapped = validateAndMapInstitutionCode($code);
    $status = '';
    
    if ($mapped === null) {
        $status = '⚠️ Mapped to NULL (will use fallback)';
    } elseif ($mapped === $code) {
        $status = '✅ No mapping needed';
    } else {
        $status = '🔄 Mapped to different code';
    }
    
    echo "<tr>";
    echo "<td><code>" . htmlspecialchars($code) . "</code></td>";
    echo "<td><code>" . htmlspecialchars($mapped ?? 'NULL') . "</code></td>";
    echo "<td>{$status}</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// Test 2: SOAP API with Fallback
echo "<div class='test-section'>";
echo "<h2>Test 2: SOAP API with Fallback Mechanism</h2>\n";

try {
    $dosarService = new DosarService();
    
    // Test the problematic institution code
    $problematicCode = 'InaltaCurtedeCASSATIESIJUSTITIE';
    $mappedCode = validateAndMapInstitutionCode($problematicCode);
    
    echo "<h3>Testing: {$problematicCode}</h3>";
    echo "<p>Mapped to: <code>" . htmlspecialchars($mappedCode ?? 'NULL') . "</code></p>";
    
    $testParams = [
        'numarDosar' => '',
        'numeParte' => '',
        'obiectDosar' => '',
        'institutie' => $mappedCode,
        'dataStart' => '01.01.2023',
        'dataStop' => '31.12.2023',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => '',
        '_maxResults' => 10
    ];
    
    $fallbackUsed = false;
    try {
        $results = $dosarService->cautareAvansata($testParams);
        echo "<div class='success'>";
        echo "<p>✅ Direct SOAP call succeeded with mapped code</p>";
        echo "<p>Results: " . count($results) . "</p>";
        echo "</div>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'not valid') !== false || strpos($e->getMessage(), 'invalid') !== false) {
            echo "<div class='warning'>";
            echo "<p>⚠️ SOAP API rejected mapped code, testing fallback...</p>";
            echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
            
            // Test fallback
            $testParams['institutie'] = null;
            try {
                $fallbackResults = $dosarService->cautareAvansata($testParams);
                $fallbackUsed = true;
                
                echo "<div class='success'>";
                echo "<p>✅ Fallback (no institution filter) succeeded</p>";
                echo "<p>Results before client-side filtering: " . count($fallbackResults) . "</p>";
                echo "</div>";
                
                // Test client-side filtering
                $filteredResults = filterResultsByInstitution($fallbackResults, $problematicCode);
                echo "<div class='info'>";
                echo "<p>📊 Results after client-side filtering: " . count($filteredResults) . "</p>";
                echo "</div>";
                
            } catch (Exception $fallbackException) {
                echo "<div class='error'>";
                echo "<p>❌ Fallback also failed: " . htmlspecialchars($fallbackException->getMessage()) . "</p>";
                echo "</div>";
            }
        } else {
            echo "<div class='error'>";
            echo "<p>❌ Unexpected SOAP error: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<p>❌ Failed to initialize DosarService: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

// Test 3: Client-side Filtering
echo "<div class='test-section'>";
echo "<h2>Test 3: Client-side Institution Filtering</h2>\n";

// Create mock results for testing
$mockResults = [
    (object)['institutie' => 'TribunalulBUCURESTI', 'numar' => '1234/2023'],
    (object)['institutie' => 'CurteadeApelBUCURESTI', 'numar' => '5678/2023'],
    (object)['institutie' => 'JudecatoriaSECTORUL1BUCURESTI', 'numar' => '9012/2023'],
    (object)['institutie' => 'TribunalulCLUJ', 'numar' => '3456/2023'],
];

echo "<p>Testing client-side filtering with mock data...</p>";
echo "<p>Mock results count: " . count($mockResults) . "</p>";

$filterTests = [
    'TribunalulBUCURESTI' => 'Should match 1 result',
    'CurteadeApelBUCURESTI' => 'Should match 1 result',
    'NonExistentCode' => 'Should match 0 results'
];

foreach ($filterTests as $filterCode => $expected) {
    $filtered = filterResultsByInstitution($mockResults, $filterCode);
    echo "<p>Filter: <code>{$filterCode}</code> → " . count($filtered) . " results ({$expected})</p>";
}

echo "</div>";

echo "<div class='test-section info'>";
echo "<h2>Summary</h2>";
echo "<ul>";
echo "<li>✅ Institution code mapping function implemented</li>";
echo "<li>✅ SOAP API fallback mechanism implemented</li>";
echo "<li>✅ Client-side filtering function implemented</li>";
echo "<li>✅ Error handling and user notifications added</li>";
echo "<li>✅ Export functions updated with validation</li>";
echo "</ul>";
echo "<p><strong>The advanced search functionality should now handle institution code errors gracefully.</strong></p>";
echo "</div>";

?>
