<?php
/**
 * Test script to validate SOAP API institution values
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<h1>SOAP API Institution Values Test</h1>\n";
echo "<style>
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
    .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
    .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .test-result { margin: 5px 0; padding: 8px; border-radius: 3px; }
</style>\n";

// Test institution codes that are commonly used
$testInstitutions = [
    'TribunalulBUCURESTI',
    'CurteadeApelBUCURESTI', 
    'InaltaCurtedeCASSATIESIJUSTITIE',
    'JudecatoriaSECTORUL1BUCURESTI',
    'TribunalulCLUJ',
    'CurteadeApelCLUJ'
];

echo "<div class='test-section info'>";
echo "<h2>Testing Institution Codes with SOAP API</h2>\n";
echo "<p>This test will check which institution codes are accepted by the SOAP API.</p>\n";
echo "</div>";

try {
    $dosarService = new DosarService();
    
    echo "<div class='test-section'>";
    echo "<h3>Test Results</h3>\n";
    echo "<table>";
    echo "<tr><th>Institution Code</th><th>Status</th><th>Results Count</th><th>Error Message</th></tr>";
    
    foreach ($testInstitutions as $institutionCode) {
        echo "<tr>";
        echo "<td><code>" . htmlspecialchars($institutionCode) . "</code></td>";
        
        try {
            $testParams = [
                'numarDosar' => '',
                'numeParte' => '',
                'obiectDosar' => '',
                'institutie' => $institutionCode,
                'dataStart' => '01.01.2023',
                'dataStop' => '31.12.2023',
                'dataUltimaModificareStart' => '',
                'dataUltimaModificareStop' => '',
                '_maxResults' => 5
            ];
            
            $results = $dosarService->cautareAvansata($testParams);
            
            if (is_array($results)) {
                echo "<td class='success'>✅ VALID</td>";
                echo "<td>" . count($results) . "</td>";
                echo "<td>-</td>";
            } else {
                echo "<td class='warning'>⚠️ UNKNOWN</td>";
                echo "<td>N/A</td>";
                echo "<td>Unexpected response type</td>";
            }
            
        } catch (Exception $e) {
            $errorMsg = $e->getMessage();
            if (strpos($errorMsg, 'not valid') !== false || strpos($errorMsg, 'invalid') !== false) {
                echo "<td class='error'>❌ INVALID</td>";
                echo "<td>0</td>";
                echo "<td>" . htmlspecialchars($errorMsg) . "</td>";
            } else {
                echo "<td class='warning'>⚠️ ERROR</td>";
                echo "<td>0</td>";
                echo "<td>" . htmlspecialchars($errorMsg) . "</td>";
            }
        }
        
        echo "</tr>";
        
        // Small delay to avoid overwhelming the API
        usleep(500000); // 0.5 seconds
    }
    
    echo "</table>";
    echo "</div>";
    
    // Test with null institution (should work)
    echo "<div class='test-section'>";
    echo "<h3>Control Test: Null Institution</h3>\n";
    
    try {
        $nullParams = [
            'numarDosar' => '',
            'numeParte' => '',
            'obiectDosar' => '',
            'institutie' => null,
            'dataStart' => '01.01.2023',
            'dataStop' => '31.12.2023',
            'dataUltimaModificareStart' => '',
            'dataUltimaModificareStop' => '',
            '_maxResults' => 10
        ];
        
        $nullResults = $dosarService->cautareAvansata($nullParams);
        
        echo "<div class='success'>";
        echo "<p>✅ Null institution test PASSED: " . count($nullResults) . " results returned</p>";
        echo "<p>This confirms the SOAP API is working and the issue is with specific institution codes.</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<p>❌ Null institution test FAILED: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p>This suggests a broader SOAP API issue.</p>";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>Critical Error</h3>";
    echo "<p>Failed to initialize DosarService: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Show the institution list from our functions
echo "<div class='test-section'>";
echo "<h3>Current Institution List (First 20 entries)</h3>\n";
echo "<p>These are the institution codes currently defined in our getInstanteList() function:</p>";

try {
    $institutii = getInstanteList();
    $codes = array_keys($institutii);
    
    echo "<table>";
    echo "<tr><th>Code</th><th>Display Name</th></tr>";
    
    for ($i = 0; $i < min(20, count($codes)); $i++) {
        $code = $codes[$i];
        echo "<tr>";
        echo "<td><code>" . htmlspecialchars($code) . "</code></td>";
        echo "<td>" . htmlspecialchars($institutii[$code]) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "<p>Total institutions in list: <strong>" . count($institutii) . "</strong></p>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<p>Error loading institution list: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

echo "<div class='test-section info'>";
echo "<h3>Next Steps</h3>";
echo "<ul>";
echo "<li>If some institution codes show as INVALID, we need to find the correct SOAP API codes</li>";
echo "<li>If all codes show as INVALID, there might be a different format expected</li>";
echo "<li>Check the SOAP WSDL documentation for valid institution values</li>";
echo "<li>Consider implementing a mapping between display names and SOAP API codes</li>";
echo "</ul>";
echo "</div>";

?>
